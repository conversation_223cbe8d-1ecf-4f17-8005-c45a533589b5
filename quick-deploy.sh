#!/bin/bash

# Quick deployment script - builds and opens AWS console
set -e

echo "🔨 Building application..."
./mvnw clean package

echo "✅ Build complete!"
echo "📁 JAR location: target/diet-app-backend-0.0.1-SNAPSHOT.jar"
echo ""
echo "🌐 Next steps:"
echo "1. Go to: https://ap-south-1.console.aws.amazon.com/elasticbeanstalk/home?region=ap-south-1#/application/overview?applicationName=diet-app"
echo "2. Click on 'Diet-app-env-1' environment"
echo "3. Click 'Upload and Deploy'"
echo "4. Upload the JAR file from target/ directory"
echo ""

# Optionally open the URL (macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🚀 Opening AWS Console..."
    open "https://ap-south-1.console.aws.amazon.com/elasticbeanstalk/home?region=ap-south-1#/application/overview?applicationName=diet-app"
fi
