package com.myfitnessjourney.backend_service.service.impl;

import com.myfitnessjourney.backend_service.dto.FoodSearchResponseDto;
import com.myfitnessjourney.backend_service.repository.FoodNutrientsRepository;
import com.myfitnessjourney.backend_service.service.FoodSearchService;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FoodSearchServiceImpl implements FoodSearchService {

    private final FoodNutrientsRepository foodNutrientsRepository;

    @Override
    public List<FoodSearchResponseDto> searchFoods(String query) {
        if (query == null || query.trim().isEmpty()) {
            return List.of();
        }

        log.debug("Searching for foods with query: {}", query);
        
        List<Object[]> results = foodNutrientsRepository.searchFoodByText(query.trim());
        
        return results.stream()
                .map(this::mapToFoodSearchResponse)
                .collect(Collectors.toList());
    }

    private FoodSearchResponseDto mapToFoodSearchResponse(Object[] row) {
        return FoodSearchResponseDto.builder()
                .code(getString(row[0]))
                .productName(getString(row[1]))
                .brandTag(getString(row[2]))
                .categoryTags(parseStringArray(row[3]))
                .countryTags(parseStringArray(row[4]))
                .imageUrl(getString(row[5]))
                .energyKcal100g(getDouble(row[6]))
                .fat100g(getDouble(row[7]))
                .proteins100g(getDouble(row[8]))
                .carbohydrates100g(getDouble(row[9]))
                .sugars100g(getDouble(row[10]))
                .salt100g(getDouble(row[11]))
                .sodium100g(getDouble(row[12]))
                .build();
    }

    private String getString(Object value) {
        return value != null ? value.toString() : null;
    }

    private Double getDouble(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof BigDecimal) {
            return ((BigDecimal) value).doubleValue();
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            log.warn("Could not parse numeric value: {}", value);
            return null;
        }
    }

    private List<String> parseStringArray(Object value) {
        if (value == null) {
            return List.of();
        }
        
        String stringValue = value.toString();
        
        // Handle PostgreSQL array format: {item1,item2,item3}
        if (stringValue.startsWith("{") && stringValue.endsWith("}")) {
            String content = stringValue.substring(1, stringValue.length() - 1);
            if (content.trim().isEmpty()) {
                return List.of();
            }
            return Arrays.stream(content.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toList());
        }
        
        return List.of(stringValue);
    }
}
