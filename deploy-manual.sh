#!/bin/bash

# Manual deployment script for diet-app-backend
# This script mimics the GitHub Actions workflow

set -e  # Exit on any error

echo "🚀 Starting manual deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="diet-app"
ENV_NAME="Diet-app-env-1"
REGION="ap-south-1"
JAR_NAME="diet-app-backend-0.0.1-SNAPSHOT.jar"

# Check if required tools are installed
command -v aws >/dev/null 2>&1 || { echo -e "${RED}❌ AWS CLI is required but not installed.${NC}" >&2; exit 1; }
command -v eb >/dev/null 2>&1 || { echo -e "${RED}❌ EB CLI is required but not installed.${NC}" >&2; exit 1; }

echo -e "${YELLOW}📦 Building application...${NC}"

# Clean and build the project
./mvnw clean verify package --file pom.xml

# Check if JAR was created
if [ ! -f "target/${JAR_NAME}" ]; then
    echo -e "${RED}❌ JAR file not found: target/${JAR_NAME}${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Build completed successfully${NC}"

# Create version label with timestamp
VERSION_LABEL="manual-deploy-$(date +%Y%m%d-%H%M%S)"

echo -e "${YELLOW}🚀 Deploying to AWS Elastic Beanstalk...${NC}"
echo -e "Application: ${APP_NAME}"
echo -e "Environment: ${ENV_NAME}"
echo -e "Version: ${VERSION_LABEL}"
echo -e "Region: ${REGION}"

# Deploy using EB CLI
eb deploy ${ENV_NAME} \
    --version-label ${VERSION_LABEL} \
    --timeout 10 \
    --region ${REGION}

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
    echo -e "${GREEN}🌐 Your application should be available at your Elastic Beanstalk URL${NC}"
else
    echo -e "${RED}❌ Deployment failed${NC}"
    exit 1
fi
