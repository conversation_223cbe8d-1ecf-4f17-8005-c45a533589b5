#!/bin/bash

# Simple AWS CLI deployment script
set -e

echo "🚀 Building and deploying with AWS CLI..."

# Build the application
./mvnw clean package

# Configuration
APP_NAME="diet-app"
ENV_NAME="Diet-app-env-1"
REGION="ap-south-1"
JAR_FILE="target/diet-app-backend-0.0.1-SNAPSHOT.jar"
VERSION_LABEL="manual-$(date +%Y%m%d-%H%M%S)"

# Create application version
echo "📦 Creating application version..."
aws elasticbeanstalk create-application-version \
    --application-name ${APP_NAME} \
    --version-label ${VERSION_LABEL} \
    --source-bundle S3Bucket="elasticbeanstalk-${REGION}-$(aws sts get-caller-identity --query Account --output text)",S3Key="${APP_NAME}/${VERSION_LABEL}.jar" \
    --region ${REGION}

# Upload JAR to S3
echo "⬆️ Uploading JAR to S3..."
aws s3 cp ${JAR_FILE} s3://elasticbeanstalk-${REGION}-$(aws sts get-caller-identity --query Account --output text)/${APP_NAME}/${VERSION_LABEL}.jar

# Deploy to environment
echo "🚀 Deploying to environment..."
aws elasticbeanstalk update-environment \
    --application-name ${APP_NAME} \
    --environment-name ${ENV_NAME} \
    --version-label ${VERSION_LABEL} \
    --region ${REGION}

echo "✅ Deployment initiated! Check AWS Console for status."
