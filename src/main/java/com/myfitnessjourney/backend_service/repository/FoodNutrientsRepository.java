package com.myfitnessjourney.backend_service.repository;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface FoodNutrientsRepository extends JpaRepository<Object, Long> {

    @Query(value = """
        SELECT code, product_name, brand_tag, category_tags, country_tags, image_url,
               energy_kcal_100g, fat_100g, proteins_100g, carbohydrates_100g,
               sugars_100g, salt_100g, sodium_100g
        FROM food_nutrients
        WHERE search_vector @@ plainto_tsquery('english', :query)
        ORDER BY ts_rank_cd(search_vector, plainto_tsquery('english', :query)) DESC
        LIMIT 20
        """, nativeQuery = true)
    List<Object[]> searchFoodByText(@Param("query") String query);
}
