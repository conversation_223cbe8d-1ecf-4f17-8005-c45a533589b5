# Food Search API

## Overview
The Food Search API provides full-text search functionality for food items in the database using PostgreSQL's advanced text search capabilities.

## Endpoint
```
GET /api/v1/foods/search?q={query}
```

## Parameters
- `q` (required): Search query string

## Response Format
Returns an array of food items matching the search query:

```json
[
  {
    "code": "123456789",
    "productName": "Red Apple",
    "brandTag": "Fresh Fruits",
    "categoryTags": ["fruits", "fresh", "organic"],
    "countryTags": ["usa", "canada"],
    "imageUrl": "http://example.com/apple.jpg",
    "energyKcal100g": 52.0,
    "fat100g": 0.2,
    "proteins100g": 0.3,
    "carbohydrates100g": 14.0,
    "sugars100g": 10.0,
    "salt100g": 0.0,
    "sodium100g": 1.0
  }
]
```

## Features
- **Full-text search**: Uses PostgreSQL's `tsvector` and `plainto_tsquery` for intelligent text matching
- **Ranked results**: Results are ordered by relevance using `ts_rank_cd`
- **Limit**: Returns up to 20 results per query
- **Fast performance**: Leverages database indexes for optimal search speed

## Example Usage

### Search for apples
```bash
curl "http://localhost:8080/api/v1/foods/search?q=apple"
```

### Search for multiple terms
```bash
curl "http://localhost:8080/api/v1/foods/search?q=organic%20apple%20juice"
```

### Search by brand
```bash
curl "http://localhost:8080/api/v1/foods/search?q=coca%20cola"
```

## Implementation Details

### Database Schema
The search functionality uses the `food_nutrients` table with the following key fields:
- `search_vector`: tsvector column containing indexed searchable text
- Full-text search index on `search_vector` for performance

### Search Algorithm
1. Query is processed using `plainto_tsquery('english', query)`
2. Matches against `search_vector` using `@@` operator
3. Results ranked by `ts_rank_cd` function
4. Limited to top 20 results

### Performance
- Uses GIN index on `search_vector` for fast lookups
- Supports complex queries with multiple terms
- Handles partial word matching and stemming

## Error Handling
- Missing query parameter returns 500 error
- Empty query returns empty array
- Invalid queries are handled gracefully

## Testing
Unit tests and integration tests are provided:
- `FoodSearchServiceTest`: Tests service layer logic
- `FoodSearchControllerIT`: Tests API endpoint behavior
